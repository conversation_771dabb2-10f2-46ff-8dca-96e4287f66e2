import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_portrait_data.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';

class PhotoPortraitCategoryPage extends ConsumerStatefulWidget {
  const PhotoPortraitCategoryPage({
    super.key,
    this.initialCategoryIndex = 0,
    this.categories,
  });

  /// 初始选中的分类索引
  final int initialCategoryIndex;
  final List<PhotoPortraitCategory>? categories;

  @override
  ConsumerState<PhotoPortraitCategoryPage> createState() =>
      _PhotoPortraitCategoryPageState();
}

class _PhotoPortraitCategoryPageState
    extends ConsumerState<PhotoPortraitCategoryPage>
    with TickerProviderStateMixin {
  // 模拟数据 - 写真分类数据（与主页面保持一致）
  List<PhotoPortraitCategory> get mockCategoryData =>
      widget.categories ?? PhotoPortraitData.mockCategoryData;

  late TabController _tabController;
  @override
  void initState() {
    // TODO: implement initState
    _tabController = TabController(
        length: mockCategoryData.length,
        vsync: this,
        initialIndex: widget.initialCategoryIndex);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final categories = mockCategoryData;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        toolbarHeight: 44.h,
        title: _buildCategoryNavigation(context, ref, categories),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () => {},
            child: const Icon(Icons.format_list_bulleted, color: Colors.white),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        physics: const BouncingScrollPhysics(),
        children: categories.map((category) {
          return _buildCategoryContent(context, category);
        }).toList(),
      ),
    );
  }

  /// 构建分类导航栏
  Widget _buildCategoryNavigation(
    BuildContext context,
    WidgetRef ref,
    List<PhotoPortraitCategory> categories,
  ) {
    final tabs = categories.map(
      (e) => _buildCategoryTab(context, e.caseName ?? ""),
    );
    return Stack(
      children: [

        TabBar(
          controller: _tabController,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          labelPadding: const EdgeInsets.symmetric(vertical: 8,horizontal: 16),
          dividerHeight: 0,
          labelStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          unselectedLabelStyle:const TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
          indicatorWeight: 1, // 隐藏默认指示器
          indicatorColor: const Color(0xFF30E6B8),
          indicatorSize: TabBarIndicatorSize.label,
          tabs: tabs.toList(),
        ),
      ],
    );
  }

  /// 构建单个分类标签
  Widget _buildCategoryTab(
    BuildContext context,
    String name,
  ) {
    return Center(
      child: Text(
        name,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.white,
        ),
      ),
    );
  }

  /// 构建分类内容
  Widget _buildCategoryContent(
      BuildContext context, PhotoPortraitCategory category) {
    final details = category.details ?? [];

    if (details.isEmpty) {
      return const Center(
        child: Text(
          "该分类暂无内容",
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: details.length,
      itemBuilder: (context, index) {
        final detail = details[index];
        return _buildCategoryDetailItem(context, detail);
      },
    );
  }

  /// 构建分类详情项
  Widget _buildCategoryDetailItem(
      BuildContext context, PhotoPortraitCategoryDetail detail) {
    return InkWell(
      onTap: () => _handleDetailItemTap(detail),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // 网络图片
              CachedNetworkImage(
                imageUrl: detail.caseImage ?? "",
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.purple.withValues(alpha: 0.6),
                        Colors.pink.withValues(alpha: 0.8),
                        Colors.orange.withValues(alpha: 0.6),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.grey.withValues(alpha: 0.6),
                        Colors.grey.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                      size: 32,
                    ),
                  ),
                ),
              ),
              // 渐变遮罩
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.6),
                    ],
                  ),
                ),
              ),
              // 标题和描述
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (detail.caseTitle?.isNotEmpty == true)
                        Text(
                          detail.caseTitle!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      if (detail.casePrompt?.isNotEmpty == true) ...[
                        const SizedBox(height: 4),
                        Text(
                          detail.casePrompt!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white70,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理详情项点击
  void _handleDetailItemTap(PhotoPortraitCategoryDetail detail) {
    // TODO: 跳转到写真生成页面
    debugPrint("选择写真: ${detail.caseTitle}，ID: ${detail.caseId}");
  }
}

/// 自定义Tab指示器
class CustomTabIndicator extends Decoration {
  final Color color;
  final double height;
  final double bottomMargin;

  const CustomTabIndicator({
    required this.color,
    this.height = 2.0,
    this.bottomMargin = 8.0,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomTabIndicatorPainter(
      color: color,
      height: height,
      bottomMargin: bottomMargin,
    );
  }
}

/// 自定义Tab指示器绘制器
class _CustomTabIndicatorPainter extends BoxPainter {
  final Color color;
  final double height;
  final double bottomMargin;

  _CustomTabIndicatorPainter({
    required this.color,
    required this.height,
    required this.bottomMargin,
  });

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Rect rect = Rect.fromLTWH(
      offset.dx,
      offset.dy + configuration.size!.height - height - bottomMargin,
      configuration.size!.width,
      height,
    );

    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawRect(rect, paint);
  }
}
