class Api {
  /// ========================账户==================///

  /// 发送验证码
  /// phone	phone
  /// https://testnovel.msmds.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/APP%E7%94%A8%E6%88%B7%E8%A1%A8%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getPhoneVerifyCodeUsingGET
  static const String getPhoneVerifyCode = "/app/appUser/getPhoneVerifyCode";

  /// 手机号登录
  /// code	验证码
  /// phone	手机号
  /// https://testnovel.msmds.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/APP%E7%94%A8%E6%88%B7%E8%A1%A8%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/phoneLoginUsingPOST
  static const String phoneLogin = "/app/appUser/phoneLogin";

  /// ========================账户==================///

  /// 语音人物列表
  /// https://testnovel.msmds.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%B0%8F%E8%AF%B4%E6%94%B9%E5%86%99%E8%AE%B0%E5%BD%95%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listVoiceUsingGET
  static const String listVoice = "/app/novelRewrite/listVoice";

  /// 合成
  /// https://testnovel.msmds.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%B0%8F%E8%AF%B4%E6%94%B9%E5%86%99%E8%AE%B0%E5%BD%95%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/saveOrUpdateByContentUsingPOST
  /// {
  ///   "aiRewriteSwitch": 0,
  ///   "category": "",
  ///   "content": "",
  ///   "createTime": "",
  ///   "eraStyle": "",
  ///   "fileName": "",
  ///   "generationState": 0,
  ///   "id": 0,
  ///   "model": 0,
  ///   "novelId": "",
  ///   "originFileUrl": "",
  ///   "paintingStyle": "",
  ///   "processState": 0,
  ///   "promptUrl": "",
  ///   "rewriteFileUrl": "",
  ///   "segmentationMaxLength": 0,
  ///   "state": 0,
  ///   "updateTime": "",
  ///   "videoUrl": "",
  ///   "voiceCode": ""
  /// }
  static const String creation = "/app/novelRewrite/saveOrUpdateByContent";

  /// 分页查询生成列表
  /// pageNo	页码数
  /// pageSize	页码大小
  static const String creationList = "/app/novelRewrite/listPage";

  /// 批量删除
  /// https://testnovel.msmds.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%B0%8F%E8%AF%B4%E6%94%B9%E5%86%99%E8%AE%B0%E5%BD%95%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/removeByIdsUsingPOST
  /// ids	删除列表
  static const String deleteByIds = "/app/novelRewrite/removeByIds";

  /// 查询用户会员
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%94%A8%E6%88%B7%E4%BC%9A%E5%91%98%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getByUserIdUsingGET
  static const String getMemberInfo = "/user/userMember/getByUserId";

  /// 分页查询算力明细
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%AE%97%E5%8A%9B%E8%B4%A6%E6%88%B7%E6%98%8E%E7%BB%86%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listPageUsingGET_8
  static const String getPowerDetailList = "/user/powerAccountDetail/listPage";

  /// 查询算力账户余额
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%94%A8%E6%88%B7%E7%AE%97%E5%8A%9B%E8%B4%A6%E6%88%B7%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getByUserIdUsingGET_1
  static const String getAccountPower = "/user/userPowerAccount/getByUserId";

  /// 查询所有会员套餐
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E4%BC%9A%E5%91%98%E5%A5%97%E9%A4%90%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listAllUsingGET
  static const String getAllPackage = "/power/memberPackage/listAll";

  /// 购买套餐
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%AE%97%E5%8A%9B%E6%94%AF%E4%BB%98%E8%AE%A2%E5%8D%95%EF%BC%88%E5%8C%85%E5%90%AB%E4%BC%9A%E5%91%98%E8%AE%A2%E5%8D%95%E5%92%8C%E5%8A%A0%E6%B2%B9%E5%8C%85%E8%AE%A2%E5%8D%95%EF%BC%89%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/buyPackageUsingPOST
  /// {
  ///   "orderType": 0,
  ///   "payType": 0,
  ///   "subType": 0,
  ///   "wechatPayType": 0
  /// }
  static const String buyPackage = "/power/powerPayOrder/buyPackage";

  /// 苹果支付通知校验
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%AE%97%E5%8A%9B%E6%94%AF%E4%BB%98%E8%AE%A2%E5%8D%95%EF%BC%88%E5%8C%85%E5%90%AB%E4%BC%9A%E5%91%98%E8%AE%A2%E5%8D%95%E5%92%8C%E5%8A%A0%E6%B2%B9%E5%8C%85%E8%AE%A2%E5%8D%95%EF%BC%89%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/applePayNotificationVerifyUsingPOST
  /// {
  ///   "environment": "",
  ///   "orderNo": "",
  ///   "paymentData": ""
  /// }
  static const String applePayVerify =
      "/power/powerPayOrder/appleSubscriptionPayBind";

  /// 查询制作视频消耗算力值
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%AE%97%E5%8A%9B%E9%A1%B9%E7%9B%AE%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getByItemTypeUsingGET
  /// itemType： 1
  static const String getByItemType = "/power/powerItem/getByItemType";

  /// 获取首页banner列表
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E9%A6%96%E9%A1%B5banner%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listHomePageBannerUsingGET
  static const String listMainBanner =
      "/intelligent/homePageBanner/listHomePageBanner";

  /// 获取所有分组
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%99%BA%E8%83%BD%E5%BA%94%E7%94%A8%E5%88%86%E7%BB%84%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listAllUsingGET
  static const String listIntelligentAppGroup =
      "/intelligent/intelligentAppGroup/listAll";

  /// 根据分组ID获取分组下的应用
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%99%BA%E8%83%BD%E5%BA%94%E7%94%A8%E5%88%86%E7%BB%84%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listByGroupIdUsingGET
  /// groupId 分组ID，不传则查所有智能应用
  static const String intelligentAppByGroupId =
      "/intelligent/intelligentAppGroup/listByGroupId";

  /// 获取扣子会话
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%89%A3%E5%AD%90%E4%BC%9A%E8%AF%9D%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getCozeConversationUsingGET
  /// botId 智能体ID
  static const String getCozeConversation =
      "/coze/cozeConversation/getCozeConversation";

  /// 查询扣子会话消息列表
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%89%A3%E5%AD%90%E4%BC%9A%E8%AF%9D%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/queryCozeConversationMessageListUsingGET
  /// conversationId 会话ID
  /// limit 条数，默认50条
  static const String queryConversationMessageList =
      "/coze/cozeConversation/queryCozeConversationMessageList";

  /// 上传文件
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/Coze%20API%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/uploadFileUsingPOST
  /// file formData
  static const String uploadFile = "/cozeApi/uploadFile";

  /// 获取扣子智能体信息
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%89%A3%E5%AD%90%E6%99%BA%E8%83%BD%E4%BD%93%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getBotInfoUsingGET
  /// botId	扣子智能体ID
  static const String getBotInfo = "/coze/cozeBot/getBotInfo";

  /// 分页查询作品明细
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%89%A3%E5%AD%90%E4%BD%9C%E5%93%81%E6%98%8E%E7%BB%86%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listPageUsingGET_2
  /// conversationId	会话ID
  /// pageNum	页码
  /// pageSize	每页数量
  ///
  static const String getWorkDetailList = "/coze/cozeBotWorkRecord/listPage";

  /// 通过chatId查询算力消耗
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%89%A3%E5%AD%90%E6%99%BA%E8%83%BD%E4%BD%93%E4%BD%9C%E5%93%81%E8%AE%B0%E5%BD%95%E6%98%8E%E7%BB%86%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getByChatIdUsingGET
  /// chatId	对话ID
  static const String getRecordPowerById =
      "/coze/cozeBotWorkRecordDetail/getByChatId";

  /// 公共数字人筛选
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%85%AC%E5%85%B1%E6%95%B0%E5%AD%97%E4%BA%BA%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getFilterUsingGET
  static const String publicDigitalFilter = "/app/publicPerson/getFilter";

  /// 公共数字人列表
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%85%AC%E5%85%B1%E6%95%B0%E5%AD%97%E4%BA%BA%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listByPageUsingGET
  /// audioName	声音名称
  /// gender	性别
  /// pageNum	页码
  /// pageSize	每页长度
  /// personId	人物id
  /// personName	人物名称
  /// personType	人物类型，传入英文代表，全身：whole_body， 头像：circle_view，坐姿：sit_body
  static const String publicDigitalList = "/app/publicPerson/listByPage";

  /// 用户创建定制视频
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%94%A8%E6%88%B7%E5%AE%9A%E5%88%B6%E8%A7%86%E9%A2%91%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/createVideoUsingPOST
  static const String customizeVideo = "/app/userCustomizeVideo/createVideo";

  /// 用户定制视频列表
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%94%A8%E6%88%B7%E5%AE%9A%E5%88%B6%E8%A7%86%E9%A2%91%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listByUserIdUsingGET_2
  static const String customizeVideoRecord = "/app/userCustomizeVideo/listByUserId";

  /// 获取定制视频所需算力
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%94%A8%E6%88%B7%E5%AE%9A%E5%88%B6%E8%A7%86%E9%A2%91%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getConsumePowerUsingGET_2
  static const String customizeVideoConsumePower = "/app/userCustomizeVideo/getConsumePower";

  /// 修改视频别名
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%94%A8%E6%88%B7%E5%AE%9A%E5%88%B6%E8%A7%86%E9%A2%91%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/updateAliasUsingPOST_2
  /// alias	别名	query	false
  /// id	id	query	false
  static const String customizeVideoUpdateAlias = "/app/userCustomizeVideo/updateAlias";

  /// 删除用户定制视频
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E7%94%A8%E6%88%B7%E5%AE%9A%E5%88%B6%E8%A7%86%E9%A2%91%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/removeVideoUsingPOST
  /// ids	ids	body array
  static const String customizeVideoDelete = "/app/userCustomizeVideo/removeVideo";

  /// 文本生成视频
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/ai%E8%A7%86%E9%A2%91%E7%94%9F%E6%88%90%E7%9B%B8%E5%85%B3/textToVideoUsingPOST
  /// {
  ///   "img1": "", 图片1（首帧视频时必填）
  ///   "img2": "", 图片2（首尾帧视频时必填）
  ///   "text": "",
  ///   "userId": 0
  /// }
  static const String textToVideo = "/api/aiVideo/textToVideo";

  /// 查询视频案例列表
  /// caseType	案例类型，1：文生视频，2：图生视频（首尾帧）
  static const String listVideoCase = "/api/aiVideo/listCase";

  /// 老照片修复
  /// {
  ///   "imgUrl": ""
  /// }
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%9B%BE%E7%89%87ai%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/oldPhotoRepairUsingPOST
  static const String oldPhotoRepair = "/image/ai/oldPhotoRepair";

  /// 图片上传
  /// file	图片	formData	file
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%9B%BE%E7%89%87%E4%B8%8A%E4%BC%A0%E8%AE%B0%E5%BD%95%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/uploadUsingPOST_1
  static const String imgUpload = "/comfyui/imgUploadRecord/upload";

  /// 照片画质修复
  /// {
  ///   "imgUrl": ""
  /// }
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%9B%BE%E7%89%87ai%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/imageEnhanceUsingPOST
  static const String qualityPhotoRepair = "/image/ai/imageEnhance";

  /// AI抠图
  /// {
  ///   "imgUrl": ""
  /// }
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%9B%BE%E7%89%87ai%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/saliencySegSubmitTaskUsingPOST
  static const String saliencySegSubmitTask = "/image/ai/saliencySegSubmitTask";

  /// AI改图
  /// {
  ///   "imgUrl": "",
  ///   "prompt": ""
  /// }
  static const String imageToImage = "/image/ai/imageToImage";

  /// 获取所有AI改图案例
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/AI%E6%94%B9%E5%9B%BE%E6%A1%88%E4%BE%8B%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listAllUsingGET_5
  static const String modificationCaseList = "/picture/aiModifyPhotoCase/listAll";

  /// 查询会员页banner
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E4%BC%9A%E5%91%98%E9%A1%B5banner%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listAllUsingGET_3
  static const String memberBannerList = "/aiapp/aiMemberBanner/listAll";

  /// 查询会员页权益显示列表
  /// https://api.dyunwl.com/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/AI%E4%BC%9A%E5%91%98%E6%9D%83%E7%9B%8A%E5%9B%BE%E6%A0%87%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listAllUsingGET_4
  static const String memberBenefitsList = "/aiapp/aiMemberRightIcon/listAll";

  /// 获取对口型案例
  /// caseType 案例类型
  static const String listLipSyncCase = "/api/lypSync/listCaseByType";

  /// 宠物唱歌
  /// {
  ///   "audioUrl": "",
  ///   "imageUrl": ""
  /// }
  static const String petSinging = "/api/lypSync/petSinging";

  /// 对口型唱歌
  /// {
  ///   "audioUrl": "",
  ///   "imageUrl": ""
  /// }
  static const String lipSyncSinging = "/api/lypSync/lipSyncSinging";

  /// 卡通数字人
  /// {
  ///   "audioUrl": "",
  ///   "imageUrl": ""
  /// }
  static const String cartoonDigimon = "/api/lypSync/cartoonDigimon";

  /// AI写真分类
  static const String photoPortraitCategory = "/aiapp/aiPortraitPhotoCase/caseList";
}
